# This is a private module implemented in C++
# As such these type stubs are overly generic, but here to allow these types
# as return types for public methods
from typing import Any, final

@final
class TrapezoidMapTriFinder:
    def __init__(self, *args, **kwargs) -> None: ...
    def find_many(self, *args, **kwargs) -> Any: ...
    def get_tree_stats(self, *args, **kwargs) -> Any: ...
    def initialize(self, *args, **kwargs) -> Any: ...
    def print_tree(self, *args, **kwargs) -> Any: ...

@final
class TriContourGenerator:
    def __init__(self, *args, **kwargs) -> None: ...
    def create_contour(self, *args, **kwargs) -> Any: ...
    def create_filled_contour(self, *args, **kwargs) -> Any: ...

@final
class Triangulation:
    def __init__(self, *args, **kwargs) -> None: ...
    def calculate_plane_coefficients(self, *args, **kwargs) -> Any: ...
    def get_edges(self, *args, **kwargs) -> Any: ...
    def get_neighbors(self, *args, **kwargs) -> Any: ...
    def set_mask(self, *args, **kwargs) -> Any: ...
